export const Images = {
  bg_onboarding: require('../../assets/images/bg_onboarding.webp'),
  bgProfile: require('../../assets/images/bgProfile.webp'),
  completed: require('../../assets/images/completed.webp'),
  boyAvt: require('../../assets/images/boy.png'),
  girlAvt: require('../../assets/images/girl.png'),
  characterSkip: require('../../assets/images/characterskip.png'),
  thought: require('../../assets/images/thought.png'),
  bgLight: require('../../assets/images/bgLight.webp'),
  bgPopupLesson: require('../../assets/images/bgLessonModal.webp'),
  btnActiveLesson: require('../../assets/images/btnActiveLesson.webp'),
  bgPopupMission: require('../../assets/images/bgMission.png'),
  btnPopupMission: require('../../assets/images/btnActiveMission.webp'),
  btnLocked: require('../../assets/images/btnLocked.webp'),
  helloman: require('../../assets/images/hellomen.png'),
  btnClockLesson: require('../../assets/images/btnClockedLesson.png'),
  brokenHeart: require('../../assets/images/BrokenHeart.png'),
  brokenHeartLeft: require('../../assets/images/brokenHeartLeft.png'),
  brokenHeartRight: require('../../assets/images/brokenHeartRight.png'),
  charactorWaiting: require('../../assets/images/charactor-waiting.png'),
  charactorHappy: require('../../assets/images/charactor-happy.png'),
  chatBubble: require('../../assets/images/chat_bubble.png'),
  avatarProfile: require('../../assets/images/avatar.png'),
  frameStreak: require('../../assets/images/frameStreak.webp'),
  boardProfile: require('../../assets/images/boardProfile.webp'),
  tabActive: require('../../assets/images/tabActive.webp'),
  tabInactive: require('../../assets/images/tabInactive.webp'),
  bgPlayGround: require('../../assets/images/bg-play-ground.webp'),
  bgRolePlay: require('../../assets/images/role-play-bg.webp'),
  bgMissionRolePlay: require('../../assets/images/bg-mission-role-play.webp'),
  rolePlayName: require('../../assets/images/role-play-name.webp'),
  rolePlayBlur: require('../../assets/images/role-play-bg-blur.webp'),
  rolePlayCharactorDefault: require('../../assets/images/role-play-charactor-default.webp'),
  rolePlayCardCharactor1: require('../../assets/images/card-chacractor-1.png'),
  rolePlayCardCharactor2: require('../../assets/images/card-chacractor-2.png'),
  rolePlayCardCharactor3: require('../../assets/images/card-chacractor-3.png'),
  rolePlayCardCharactor4: require('../../assets/images/card-chacractor-4.png'),
  rolePlayCardCharactor5: require('../../assets/images/card-chacractor-5.png'),
  bgConfirmChooseCard: require('../../assets/images/bg-confirm-choose-card.png'),
  bgTopicCard: require('../../assets/images/bg-topic-card.webp'),
  bgComplete: require('../../assets/images/completedQuest.webp'),
  frameText: require('../../assets/images/frameText.png'),
  doAgain: require('../../assets/images/doAgain.png'),
  btnReview: require('../../assets/images/btnpreview.png'),
  completeQ: require('../../assets/images/complete.png'),
  missionRolePlayFrame: require('../../assets/images/mission-roleplay.webp'),
  subFrameRolePlay: require('../../assets/images/sub-frame-role-play.png'),
  startNowRolePlay: require('../../assets/images/start-now-role-play.webp'),
  closeContentRolePlay: require('../../assets/images/close-content-role-play.png'),
  boardName: require('../../assets/images/boardName.webp'),
  bgPractice: require('../../assets/images/bg-practice.webp'),
  bgRolePlayCompleted: require('../../assets/images/bgRolePlayComplete.webp'),
  practiceHeaderBanner: require('../../assets/images/practice_header_banner.webp'),
  practiceMistakeCard: require('../../assets/images/practice_mistake_card.webp'),
  practiceSkillCard: require('../../assets/images/practice_skill_card.webp'),
  topicCard1: require('../../assets/images/topic-card-1.webp'),
  topicCard2: require('../../assets/images/topic-card-2.webp'),
  topicCard3: require('../../assets/images/topic-card-3.webp'),
  topicCard4: require('../../assets/images/topic-card-4.webp'),
  topicCard5: require('../../assets/images/topic-card-5.webp'),
  topicCard6: require('../../assets/images/topic-card-6.webp'),
  topicCard7: require('../../assets/images/topic-card-7.webp'),
  randomTopCard: require('../../assets/images/randomTopCard.webp'),
  mypet: require('../../assets/images/mypet.png'),
  subFrameTopicCard: require('../../assets/images/sub-frame-topic-card.png'),
};
