import {useEffect, useRef, useState} from 'react';
import Sound from 'react-native-sound';
import HapticFeedback from 'react-native-haptic-feedback';
import {API_INTEGRATE_URL} from '@env';

Sound.setCategory('Playback');

const useSound = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const mainSoundRef = useRef<Sound | null>(null);

  const stopAndRelease = (ref: React.MutableRefObject<Sound | null>) => {
    if (ref.current) {
      ref.current.stop();
      ref.current.release();
      ref.current = null;
    }
  };

  const changeSource = (uri: string) => {
    stopAndRelease(mainSoundRef);
    const fullUri = `${API_INTEGRATE_URL}files/get-by-path?path=${uri}`;
    mainSoundRef.current = new Sound(fullUri, '', error => {
      if (error) {
        console.log('Không thể load URL:', error);
        setIsPlaying(false);
        return;
      }
      play();
    });
  };

  const play = () => {
    if (!mainSoundRef.current) return;
    mainSoundRef.current.play(success => {
      if (!success) console.log('Playback failed');
      setIsPlaying(false);
    });
    setIsPlaying(true);
  };

  const pause = () => {
    mainSoundRef.current?.pause();
    setIsPlaying(false);
  };

  const stop = () => {
    stopAndRelease(mainSoundRef);
    setIsPlaying(false);
  };

  const playLocalFile = (filename: string, activeHaptic = false) => {
    if (activeHaptic) {
      HapticFeedback.trigger('notificationSuccess', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }
    stopAndRelease(mainSoundRef);
    mainSoundRef.current = new Sound(
      `${filename}.mp3`,
      Sound.MAIN_BUNDLE,
      error => {
        if (error) {
          console.log('playLocalFile() lỗi:', error);
          return;
        }
        play();
      },
    );
  };

  useEffect(() => {
    return () => {
      stopAndRelease(mainSoundRef);
    };
  }, []);

  return {
    isPlaying,
    changeSource,
    play,
    pause,
    stop,
    playLocalFile,
  };
};

export default useSound;
