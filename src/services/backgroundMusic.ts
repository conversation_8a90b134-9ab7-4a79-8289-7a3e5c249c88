import Sound from 'react-native-sound';

Sound.setCategory('Playback');

let bgSound: Sound | null = null;
let currentFile: string | null = null;

const loadAndPlay = (filename: string) => {
  bgSound = new Sound(filename, Sound.MAIN_BUNDLE, error => {
    if (error) {
      return;
    }

    if (typeof bgSound?.setNumberOfLoops === 'function') {
      bgSound.setNumberOfLoops(-1);
      bgSound?.setVolume(0.6);
    }

    bgSound?.play(success => {
      if (!success) {
        console.log('Background music play failed');
      }
    });
  });
  currentFile = filename;
};

const play = (filename: string) => {
  if (bgSound && currentFile === filename) {
    return;
  }
  if (bgSound) {
    bgSound.stop();
    bgSound.release();
    bgSound = null;
  }
  loadAndPlay(filename);
};

const pause = () => {
  bgSound?.pause();
};

const resume = () => {
  if (bgSound) {
    bgSound.play();
  } else if (currentFile) {
    loadAndPlay(currentFile);
  }
};

const stop = () => {
  if (bgSound) {
    bgSound.stop();
    bgSound.release();
    bgSound = null;
  }
};

export default {
  play,
  pause,
  resume,
  stop,
};
