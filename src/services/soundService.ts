import Sound from 'react-native-sound';
import HapticFeedback from 'react-native-haptic-feedback';
import {API_INTEGRATE_URL} from '@env';

Sound.setCategory('Playback');

let mainSound: Sound | null = null;
let isPlaying: boolean = false;

const stopAndRelease = () => {
  if (mainSound) {
    mainSound.stop();
    mainSound.release();
    mainSound = null;
  }
  isPlaying = false;
};

const changeSource = (uri: string) => {
  stopAndRelease();
  const fullUri = `${API_INTEGRATE_URL}files/get-by-path?path=${uri}`;
  mainSound = new Sound(fullUri, '', error => {
    if (error) {
      console.log('Không thể load URL:', error);
      isPlaying = false;
      return;
    }
    play();
  });
};

const play = () => {
  if (!mainSound) return;
  mainSound.play(success => {
    if (!success) console.log('Playback failed');
    isPlaying = false;
  });
  isPlaying = true;
};

const pause = () => {
  mainSound?.pause();
  isPlaying = false;
};

const stop = () => {
  stopAndRelease();
};

const playLocalFile = (filename: string, activeHaptic = false) => {
  if (activeHaptic) {
    HapticFeedback.trigger('notificationSuccess', {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    });
  }
  stopAndRelease();
  mainSound = new Sound(`${filename}.mp3`, Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.log('playLocalFile() lỗi:', error);
      return;
    }
    play();
  });
};

export default {
  changeSource,
  play,
  pause,
  stop,
  playLocalFile,
};
