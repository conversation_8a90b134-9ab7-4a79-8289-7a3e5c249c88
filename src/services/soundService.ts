import Sound from 'react-native-sound';
import HapticFeedback from 'react-native-haptic-feedback';
import {API_INTEGRATE_URL} from '@env';
import {AppState} from 'react-native';

Sound.setCategory('Playback');

let mainSound: Sound | null = null;
let isPlaying: boolean = false;

// Lắng nghe AppState để dừng âm thanh khi app không active
AppState.addEventListener('change', (nextAppState) => {
  if (nextAppState !== 'active') {
    stopAndRelease();
  }
});

const stopAndRelease = () => {
  if (mainSound) {
    mainSound.stop();
    mainSound.release();
    mainSound = null;
  }
  isPlaying = false;
};

const changeSource = (uri: string) => {
  stopAndRelease();
  const fullUri = `${API_INTEGRATE_URL}files/get-by-path?path=${uri}`;
  mainSound = new Sound(fullUri, '', error => {
    if (error) {
      console.log('Không thể load URL:', error);
      isPlaying = false;
      return;
    }
    play();
  });
};

const play = () => {
  if (!mainSound) return;
  mainSound.play(success => {
    if (!success) console.log('Playback failed');
    isPlaying = false;
  });
  isPlaying = true;
};

const pause = () => {
  mainSound?.pause();
  isPlaying = false;
};

const stop = () => {
  stopAndRelease();
};

const playLocalFile = (filename: string, activeHaptic = false) => {
  if (activeHaptic) {
    HapticFeedback.trigger('notificationSuccess', {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    });
  }
  stopAndRelease();
  mainSound = new Sound(`${filename}.mp3`, Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.log('playLocalFile() lỗi:', error);
      return;
    }
    play();
  });
};

const playLocalFileWithHaptic = (filename: string) => {
  let beatMap = [
    0.26702947845804986, 0.3599092970521542, 0.4179591836734694,
    0.592108843537415, 1.4164172335600906,
  ];

  stopAndRelease();
  mainSound = new Sound(`${filename}.mp3`, Sound.MAIN_BUNDLE, error => {
    if (error) {
      console.log('playLocalFileWithHaptic lỗi:', error);
      return;
    }

    mainSound?.play(success => {
      if (!success) console.log('Playback failed');
      isPlaying = false;
    });
    isPlaying = true;

    const triggered = new Set<number>();
    const startTime = Date.now();

    const interval = setInterval(() => {
      const elapsedSec = (Date.now() - startTime) / 1000;

      beatMap.forEach(time => {
        if (elapsedSec >= time && !triggered.has(time)) {
          triggered.add(time);
          HapticFeedback.trigger('impactHeavy', {
            enableVibrateFallback: true,
            ignoreAndroidSystemSettings: false,
          });
        }
      });

      if (elapsedSec > Math.max(...beatMap) + 1) {
        clearInterval(interval);
      }
    }, 30);
  });
};

export default {
  changeSource,
  play,
  pause,
  stop,
  playLocalFile,
  playLocalFileWithHaptic,
};
