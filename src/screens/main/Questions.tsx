import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import QuestionContent from '../../components/QuestionContent.tsx';
import Pagination from '../../components/Pagination.tsx';
import ModalQuestion from '../../components/ModalQuestion.tsx';
import SkipQuestionModal from '../../components/modal/SkipQuestionModal.tsx';

import {useBrokenHeart} from '../../components/Brokenheart.tsx';

import {Theme} from '../../themes';
import {useReduxDispatch, useTypedSelector} from '../../redux/store.ts';
import {
  hiddenAnswer,
  hideModalQuestion,
  setDoneQuestion,
  setHeart,
  setTimeOutQuest,
} from '../../redux/reducer/QuestionSlice.ts';
import {
  fetchDoneLesson,
  fetchLessonAnswer,
} from '../../redux/reducer/fetchData.ts';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType.ts';
import {LoadResource} from '../../components/LoadResource.tsx';
import {HeaderProgress} from '../../components/HeaderProgress.tsx';
import {QuotesModal} from '../../components/QuotesModal.tsx';
import useSound from '../../hooks/useSound.ts';

type QuestionProps = NativeStackScreenProps<
  RootStackParamList,
  APP_SCREEN.QUESTIONS
>;

const getModalContent = (heart: number) => ({
  title: heart === 0 ? 'Hearts all gone!' : 'Time’s over!',
  content:
    heart === 0 ? 'Come back stronger next time!' : 'Don’t worry. Try again!',
  btnText: 'Continue',
});

const Questions: React.FC<QuestionProps> = ({route}) => {
  const {item, isDone} = route.params;
  const dispatch = useReduxDispatch();
  const {loading, dataAnswer} = useTypedSelector(state => state.lesson);
  const {isShowQuotes, isShowAnswer, dataFinish, answer, heart, isTimeOut} =
    useTypedSelector(state => state.question);
  const {BrokenHeartView, showBrokenHeart} = useBrokenHeart();

  const [data, setData] = useState<any>(item?.data);
  const [currentPage, setCurrentPage] = useState(0);
  const [done, setDone] = useState(false);
  const [preview, setPreview] = useState(isDone);
  const [visibleSkip, setVisibleSkip] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const {stop} = useSound();
  const initialTimerSeconds = useMemo(
    () => item?.duration * 60,
    [item?.duration],
  );

  const currentSecondsLeftRef = useRef(initialTimerSeconds);
  const uniqueData = Array.from(
    new Map(dataFinish.map(item => [item.id, item])).values(),
  );
  const passed = uniqueData.filter(i => i.isPassExcercise)?.length || 0;
  const isTimerActive = !done && !preview;

  const handleTimerUpdate = useCallback((sec: number) => {
    currentSecondsLeftRef.current = sec;
    if (sec === 1) {
      setVisibleSkip(true);
    }
  }, []);

  const handleEnd = useCallback(() => {
    setDone(true);
    handleDone();
    setVisibleSkip(false);
    dispatch(setTimeOutQuest(false));
  }, []);

  const handleDone = useCallback(() => {
    const form = {
      id: item?.id,
      unitId: item?.unitId,
      index: item?.index,
      isDone: true,
      data: uniqueData.map(q => ({
        id: q.id,
        index: q.index,
        isPassExcercise: q.isPassExcercise,
        answerByStudent: q.answerByStudent,
      })),
      completionTime: currentSecondsLeftRef.current,
      exAssignId: item?.exAssignId,
      numOfExcercises: item?.numOfExcercises,
      deadline: item?.deadline,
      duration: item?.duration,
      attemptLimit: item?.attemptLimit,
    };
    dispatch(fetchDoneLesson(form));
  }, [dataFinish]);

  const handleNextPage = useCallback(() => {
    dispatch(hideModalQuestion());
    if (currentPage < data?.length - 1) {
      setCurrentPage(prev => prev + 1);
    } else {
      setDone(true);
      setCurrentPage(0);
    }
  }, [currentPage, data?.length]);

  const handleNextPagination = useCallback((index: number) => {
    setCurrentPage(index);
    dispatch(hiddenAnswer());
  }, []);

  const fetchDataLessonAnswer = useCallback(async () => {
    const res = await dispatch(
      fetchLessonAnswer({
        assignId: item.exAssignId,
        lessonId: item.id,
        isHighest: isDone,
      }),
    );
    setData(res.payload.data.data);
  }, [item]);

  useEffect(() => {
    stop();
    dispatch(setDoneQuestion(preview));
    dispatch(setHeart(item?.mistakeLimit));

    if (preview) {
      fetchDataLessonAnswer();
    }
  }, [preview]);

  useEffect(() => {
    if (heart === 0) {
      setVisibleSkip(true);
    }
    if (isShowQuotes && !dataFinish[currentPage]?.isPassExcercise) {
      showBrokenHeart();
    }
  }, [heart, isShowQuotes, dataFinish]);
  useEffect(() => {
    if (done) {
      handleDone();
    }
  }, [done, dataFinish?.length, data?.length]);

  if (loading) {
    return (
      <View style={styles.container}>
        <LoadResource />
      </View>
    );
  }

  // if (!data?.length) {
  //   return (
  //     <View>
  //       <Header title="" />
  //     </View>
  //   );
  // }

  const modalContent = getModalContent(heart);

  return (
    <View style={styles.container}>
      {!done && (
        <HeaderProgress
          step={currentPage + 1}
          total={data.length}
          steps={data.length}
          height={18}
          isReview={preview}
          initialSeconds={initialTimerSeconds}
          completionTime={dataAnswer?.completionTime}
          isTimerActive={isTimerActive}
          onPressRight={() => {}}
          heart={heart}
          onTimerUpdate={handleTimerUpdate}
        />
      )}

      <QuestionContent
        currentPage={currentPage}
        done={done}
        data={data}
        passed={passed}
        total={item?.data?.length}
        onPreview={() => {
          setPreview(true);
          setDone(false);
          setCurrentPage(0);
        }}
        styles={styles}
      />

      {preview && <Pagination data={data} onPress={handleNextPagination} />}
      {!done && !preview && answer && (
        <QuotesModal
          isVisible={isShowQuotes}
          onContinue={handleNextPage}
          answer={answer}
        />
      )}
      {answer && <ModalQuestion isVisible={isShowAnswer} answer={answer} />}
      {!done && !preview && BrokenHeartView}

      <SkipQuestionModal
        {...modalContent}
        isVisible={visibleSkip || (isTimeOut && !preview)}
        onClose={() => setVisibleSkip(false)}
        onConfirm={handleEnd}
        doNotShowAgain={dontShowAgain}
        onToggleDoNotShowAgain={setDontShowAgain}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.white,
  },
  centerBox: {
    flex: 1,
    alignItems: 'center',
  },
  lottie: {
    width: 300,
    height: 300,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    bottom: 50,
    position: 'absolute',
    width: '100%',
  },
  btnFooter: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 5,
    marginRight: 8,
  },
});

export default Questions;
