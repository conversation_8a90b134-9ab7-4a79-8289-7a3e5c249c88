import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {SvgIcons} from '../../assets/svg';
import useSound from '../hooks/useSound';
import {useTheme} from '../hooks/useTheme';
import {EnhancedDataFinish} from '../types/answer.types';
import {SCREEN_WIDTH} from '../utils/Scale';
import {CustomBottomSheet} from './BottomSheet';
import Button from './Button';
import TextApp from './TextApp';
import {StarDisplay} from './Star';

type QuotesModalProps = {
  isVisible: boolean;
  onContinue: () => void;
  answer: EnhancedDataFinish | undefined;
};

export const QuotesModal = ({
  isVisible,
  answer,
  onContinue,
}: QuotesModalProps) => {
  const theme = useTheme();
  const {playLocalFile} = useSound();
  const didPlayRef = useRef(false);

  const [resultInfo, setResultInfo] = useState<{
    type: string;
    messageText: string;
    soundKey: string;
    starCount?: number;
    showStars?: boolean;
  } | null>(null);

  useEffect(() => {
    if (!answer || answer.accuracy === undefined || !answer.excerciseType)
      return;

    const {accuracy, excerciseType, answerByStudent} = answer;

    const isPronunciation = excerciseType === 'pronunciation';
    let info = {
      type: '',
      messageText: '',
      soundKey: '',
      starCount: 0,
      showStars: isPronunciation,
    };

    if (isPronunciation && Array.isArray(answerByStudent?.word_scores)) {
      const numWords = answerByStudent.word_scores.length;
      const numCorrect = answerByStudent.word_scores.filter(
        (w: any) => w.score_grade === 'A' || w.score_grade === 'B',
      ).length;

      if (numCorrect === numWords) {
        info = {
          ...info,
          type: 'correct',
          messageText: 'Excellent!',
          soundKey: 'correct_answer',
          starCount: 5,
        };
      } else if (numCorrect >= Math.ceil(numWords * 0.5)) {
        info = {
          ...info,
          type: 'almost_correct',
          messageText: 'Almost correct!',
          soundKey: 'almost_correct_answer',
          starCount: 3,
        };
      } else {
        info = {
          ...info,
          type: 'incorrect',
          messageText: 'Wrong!',
          soundKey: 'incorrect_answer',
          starCount: 0,
        };
      }
    } else {
      if (accuracy === 100) {
        info = {
          ...info,
          type: 'correct',
          messageText: 'Correct!',
          soundKey: 'correct_answer',
          showStars: false,
        };
      } else if (accuracy >= 75) {
        info = {
          ...info,
          type: 'almost_correct',
          messageText: 'Almost correct!',
          soundKey: 'almost_correct_answer',
          showStars: false,
        };
      } else {
        info = {
          ...info,
          type: 'incorrect',
          messageText: 'Wrong!',
          soundKey: 'incorrect_answer',
          showStars: false,
        };
      }
    }

    setResultInfo(info);
  }, [answer]);

  useEffect(() => {
    if (isVisible && resultInfo && !didPlayRef.current) {
      playLocalFile(resultInfo.soundKey, true);
      didPlayRef.current = true;
    } else if (!isVisible) {
      didPlayRef.current = false;
    }
  }, [isVisible, resultInfo, playLocalFile]);

  if (!resultInfo) return null;

  const {type, messageText, showStars, starCount} = resultInfo;

  return (
    <CustomBottomSheet
      visible={isVisible}
      containerStyle={[
        styles.container,
        {
          backgroundColor:
            type === 'correct'
              ? theme.bg_success_primary
              : type === 'almost_correct'
                ? theme.bg_warning_primary
                : theme.bg_error_primary,
        },
      ]}>
      <View style={{flexDirection: 'row', paddingBottom: 30}}>
        <View style={{position: 'absolute', left: -10, top: -50}}>
          {type === 'correct' && (
            <SvgIcons.BoyHappy width={97.3} height={162} />
          )}
          {type === 'almost_correct' && (
            <SvgIcons.BoySad width={97.3} height={162} />
          )}
          {type === 'incorrect' && (
            <SvgIcons.BoyCry width={97.3} height={162} />
          )}
        </View>
        <View
          style={{
            flex: 1,
            marginTop: moderateVerticalScale(15),
            marginLeft: scale(50),
            alignItems: 'center',
          }}>
          <TextApp
            preset="text_lg_semibold"
            text={messageText}
            textColor={
              type === 'correct'
                ? theme.text_success_primary
                : type === 'almost_correct'
                  ? theme.text_warning_primary
                  : theme.text_error_primary
            }
            style={{lineHeight: 20, textAlign: 'center'}}
          />
          {showStars && <StarDisplay rating={starCount || 0} />}
          <Button
            title={'Continue'}
            onPress={onContinue}
            style={{
              height: moderateVerticalScale(40),
              width: '80%',
              marginTop: moderateVerticalScale(15),
              backgroundColor:
                type === 'correct'
                  ? theme.bg_success_solid
                  : type === 'almost_correct'
                    ? theme.bg_warning_solid
                    : theme.bg_error_solid,
            }}
            textColor={theme.text_white}
            textStyle={{lineHeight: 20}}
          />
        </View>
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    width: SCREEN_WIDTH,
    paddingBottom: 0,
  },
  flex1: {
    flex: 1,
  },
});
