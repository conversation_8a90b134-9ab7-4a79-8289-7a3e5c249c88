import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useMemo,
  useRef,
  useEffect,
} from 'react';
import {Animated, StyleSheet, TouchableOpacity, View} from 'react-native';
import {scale} from 'react-native-size-matters';
import {BubbleTail} from '../../../assets/svgIcons/BubbleTail';
import TextApp from '../TextApp';
import IconVolume from '../../../assets/svgIcons/IconVolume';
import useSound from '../../hooks/useSound';

export type SpeechBubbleRef = {
  show: (content: string, isIdea?: boolean, audioPath?: string) => void;
  hide: () => void;
  updateContent: (content: string) => void;
};

const MAX_BUBBLE_WIDTH = scale(212);
const BUBBLE_MARGIN = scale(20);

type SpeechBubbleProps = {
  startIdleTimeout?: () => void;
  cancelIdleTimeout?: () => void;
};

const SpeechBubbleComponent = forwardRef<SpeechBubbleRef, SpeechBubbleProps>(
  ({startIdleTimeout, cancelIdleTimeout}, ref) => {
    const [content, setContent] = useState<string>(
      'This is our Role Play stage!',
    );
    const [key, setKey] = useState<number>(0);
    const [visible, setVisible] = useState<boolean>(false);
    const [isIdea, setIsIdea] = useState<boolean>(false);
    const [audioPath, setAudioPath] = useState<string>('');
    const {changeSource, stop, playLocalFile} = useSound();

    const scaleAnim = useRef(new Animated.Value(0.5)).current;
    const opacityAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      const timeout = setTimeout(() => {
        setVisible(true);
        playLocalFile('this_is_our_role_play_stage', false);
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
        startIdleTimeout?.();
      }, 500);
      return () => {
        clearTimeout(timeout);
        cancelIdleTimeout?.();
        stop();
      };
    }, []);

    const showBubble = useCallback(() => {
      setVisible(true);
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }, [scaleAnim, opacityAnim]);

    const hideBubble = useCallback(() => {
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0.5,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setVisible(false);
        setContent('');
      });
    }, [scaleAnim, opacityAnim]);

    useImperativeHandle(
      ref,
      () => ({
        show(newContent: string, idea: boolean = false, audio?: string) {
          setContent(newContent);
          setIsIdea(idea);
          setAudioPath(audio || '');
          setKey(prev => prev + 1);
          showBubble();
        },
        hide() {
          hideBubble();
        },
        updateContent(newContent: string) {
          setContent(newContent);
        },
      }),
      [showBubble, hideBubble],
    );

    const containerStyle = useMemo(
      () => [styles.container, isIdea && styles.containerIdea],
      [isIdea],
    );

    const animatedStyle = useMemo(
      () => [
        styles.bubble,
        {
          transform: [{scale: scaleAnim}],
          opacity: opacityAnim,
          width: MAX_BUBBLE_WIDTH,
        },
      ],
      [scaleAnim, opacityAnim],
    );

    if (!visible) return null;

    return (
      <View style={containerStyle}>
        <Animated.View style={animatedStyle}>
          <TextApp
            key={key}
            preset="text_md_medium"
            text={content}
            style={styles.content}
          />
          {audioPath && (
            <TouchableOpacity
              style={styles.speakBtn}
              onPress={() => changeSource(audioPath)}>
              <IconVolume />
            </TouchableOpacity>
          )}
          <View style={[styles.tailWrapper, isIdea && styles.tailWrapperIdea]}>
            <BubbleTail isIdea={isIdea} />
          </View>
        </Animated.View>
      </View>
    );
  },
);

export const SpeechBubble = React.memo(SpeechBubbleComponent);

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: '65%',
    left: BUBBLE_MARGIN,
    right: BUBBLE_MARGIN,
    alignItems: 'center',
    zIndex: 1,
    justifyContent: 'center',
  },
  containerIdea: {
    bottom: '70%',
  },
  bubble: {
    backgroundColor: 'white',
    borderRadius: 8,
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowOffset: {width: 0, height: 4},
    shadowRadius: 8,
    elevation: 5,
    alignSelf: 'center',
    flexShrink: 1,
    flexGrow: 0,
  },
  content: {
    color: '#000',
    textAlign: 'center',
    lineHeight: scale(20),
    flexWrap: 'wrap',
    fontSize: scale(14),
  },
  speakBtn: {
    alignSelf: 'flex-end',
  },
  tailWrapper: {
    position: 'absolute',
    bottom: scale(-12),
    alignSelf: 'center',
    transform: [{rotateY: '180deg'}],
  },
  tailWrapperIdea: {
    bottom: scale(-52),
    right: scale(60),
    transform: [{rotateY: '360deg'}],
  },
});
