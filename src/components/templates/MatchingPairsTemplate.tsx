import React, {useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {moderateScale, moderateVerticalScale} from 'react-native-size-matters';
import ButtonFooter from '../ButtonFooter.tsx';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import useSound from '../../hooks/useSound.ts';
import {useTheme} from '../../hooks/useTheme.ts';
import ItemMatching from '../item/ItemMatching.tsx';
import Animated, {
  measure,
  runOnJS,
  runOnUI,
  useAnimatedRef,
} from 'react-native-reanimated';
import Svg, {Path} from 'react-native-svg';
import {mergeByKeyString} from '../../utils/mergeByKey.ts';
import TextApp from '../TextApp/index.tsx';
import {isAndroid} from '../../utils/Scale.ts';
import {useTypedSelector} from '../../redux/store.ts';

interface ItemType {
  key: string;
  label?: string;
  audio?: string;
  image?: string;
}

interface MatchingPairsTemplateProps {
  pairLst: any;
  title: string;
  question?: string;
  answer: string;
  id: string;
  index: string | number;
  answerByStudent?: any;
  exerciseType: ExerciseType;
  mediaType: string;
}

const checkResult = (arr: any, key: string) => {
  const arrCheck = arr?.filter(
    (item: any) => item?.left?.key === key || item?.right?.key === key,
  );
  if (arrCheck[0]?.matched) {
    return true;
  } else {
    return false;
  }
};

const MatchingPairsTemplate: React.FC<MatchingPairsTemplateProps> = ({
  answerByStudent,
  id,
  index,
  pairLst,
  title,
  exerciseType,
  mediaType,
}) => {
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const leftItems: ItemType[] = answerByStudent
    ? answerByStudent?.pairLst?.left
    : pairLst.left;

  const rightItems: ItemType[] = answerByStudent
    ? answerByStudent?.pairLst?.right
    : pairLst.right;
  const [connections, setConnections] = useState<any[]>([]);
  const [selectedLeft, setSelectedLeft] = useState<any>(null);
  const [result, setResult] = useState<boolean>(false);
  const [left, setLeft] = useState<any[]>([]);
  const [right, setRight] = useState<any[]>([]);
  const [isTrue, setIsTrue] = useState<any>(null);
  const leftRefs = useRef(
    leftItems?.map(() => useAnimatedRef<Animated.View>()),
  );
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const rightRefs = useRef(
    rightItems?.map(() => useAnimatedRef<Animated.View>()),
  );
  const theme = useTheme();

  const {changeSource} = useSound();
  useEffect(() => {
    const timeout = setTimeout(() => {
      runOnUI(() => {
        const pLeft = leftRefs.current.map((ref, index) => {
          const m = measure(ref);
          return m ? {x: m.pageX, y: m.pageY, key: leftItems[index].key} : null;
        });
        const pRight = rightRefs.current.map((ref, index) => {
          const m = measure(ref);
          return m
            ? {x: m.pageX, y: m.pageY, key: rightItems[index].key}
            : null;
        });

        if (pLeft.every(Boolean)) {
          runOnJS(setLeft)(pLeft);
        }
        if (pRight.every(Boolean)) {
          runOnJS(setRight)(pRight);
        }
      })();
    }, 400);

    return () => clearTimeout(timeout);
  }, []);
  useEffect(() => {
    if (answerByStudent && left.length > 0 && right.length > 0) {
      setConnections(
        mergeByKeyString(left, right, answerByStudent.matchedKeys),
      );
      setResult(true);
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [left, right]);
  const handlePlayAudio = useCallback(
    (audio?: string) => {
      if (audio) {
        changeSource(audio);
      }
    },
    [changeSource],
  );

  const isKeyInConnections = useCallback(
    (key: string, side: 'left' | 'right') => {
      return connections.some(conn => conn[side].key === key);
    },
    [connections],
  );

  const removeConnectionByKey = useCallback(
    (key: string, side: 'left' | 'right') => {
      setConnections(prev => prev.filter(conn => conn[side].key !== key));
    },
    [],
  );

  const handleLeftPress = useCallback(
    (key: string, idx: number, audio?: string) => {
      handlePlayAudio(audio);

      if (result) {
        CheckAnswer(key);
        return;
      }

      if (isKeyInConnections(key, 'left')) {
        removeConnectionByKey(key, 'left');
        return;
      }

      setSelectedLeft({...left[idx], key});
    },
    [handlePlayAudio, isKeyInConnections, removeConnectionByKey, left],
  );

  const handleRightPress = useCallback(
    (key: string, idx: number, audio?: string) => {
      handlePlayAudio(audio);
      if (result) {
        CheckAnswer(key);
        return;
      }
      if (isKeyInConnections(key, 'right')) {
        removeConnectionByKey(key, 'right');
        return;
      }

      if (selectedLeft) {
        setConnections(prev => {
          const filtered = prev.filter(
            conn => conn.left.key !== selectedLeft.key,
          );
          return [
            ...filtered,
            {left: selectedLeft, right: {...right[idx], key}},
          ];
        });
        setSelectedLeft(null);
      }
    },
    [
      handlePlayAudio,
      isKeyInConnections,
      removeConnectionByKey,
      selectedLeft,
      right,
    ],
  );

  const CheckAnswer = useCallback(
    (key: string) => {
      const leftItem = left.find(m => m.key === key);
      const rightItem = right.find(m => m.key === key);

      setIsTrue({
        key,
        left: leftItem ?? null,
        right: rightItem ?? null,
      });

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        setIsTrue(null);
        timeoutRef.current = null;
      }, 2000);
    },
    [left, right],
  );

  const matchResult = connections.map(item => ({
    ...item,
    matched: item.left.key === item.right.key,
  }));

  const totalMatched = matchResult.filter(item => item.matched).length;

  const handleSubmit = () => {
    setConnections(matchResult);
    handleCheckAnswer(
      {
        matchedKeys: matchResult.map(item => ({
          leftKey: item.left.key,
          rightKey: item.right.key,
          isCorrect: item.matched,
        })),
        pairLst,
      },
      matchResult.filter(item => item.matched).length == connections.length,
      id,
      index,
      exerciseType,
      totalMatched.toString(),
    );

    setResult(true);
  };

  const getPath = (
    startX: number,
    fromY: number,
    endX: number,
    toY: number,
  ) => {
    return `M${startX},${fromY} L${endX},${toY}`;
  };
  const {handleCheckAnswer} = useQuestion();

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone
            ? moderateVerticalScale(isAndroid ? 130 : 150)
            : moderateVerticalScale(130),
        },
      ]}>
      <TextApp
        preset="text_md_semibold"
        textColor={theme.text_secondary}
        style={{
          textAlign: 'center',
          marginTop: isDone ? moderateVerticalScale(10) : 0,
        }}
        text={title}
      />

      <Svg style={StyleSheet.absoluteFill}>
        {connections.map((conn: any, index) => {
          const path = getPath(
            conn.left.x,
            conn.left.y,
            conn.right.x + 2,
            conn.right.y,
          );

          return (
            <Path
              key={index}
              d={path}
              stroke={
                isTrue
                  ? theme.fg_disabled_subtle
                  : result && !conn?.matched
                    ? theme.fg_error_primary
                    : conn?.matched
                      ? theme.fg_success_primary
                      : theme.fg_brand_secondary
              }
              strokeWidth={2}
              fill="none"
            />
          );
        })}
      </Svg>

      {isTrue?.left?.x && (
        <Svg style={StyleSheet.absoluteFill}>
          <Path
            key={index}
            d={getPath(
              isTrue?.left.x,
              isTrue?.left.y,
              isTrue?.right.x + 2,
              isTrue?.right.y,
            )}
            strokeDasharray="8,5"
            stroke={theme.fg_success_primary}
            strokeWidth={3}
            fill="none"
          />
        </Svg>
      )}

      <View style={styles.rowContainer}>
        <View style={styles.column}>
          {leftItems.map((item, idx) => {
            const isSelect = selectedLeft?.key === item.key;
            const isChoose = connections.some(
              conn => conn.left.key === item.key,
            );

            return (
              <ItemMatching
                ref={leftRefs.current[idx]}
                position={'left'}
                disable={checkResult(connections, item.key) && result}
                onPress={() => handleLeftPress(item.key, idx, item.audio)}
                key={item.key}
                url={item.image}
                text={item.label || ''}
                keyItem={item.key}
                audio={item.audio}
                isSelected={isSelect || isChoose}
                isCorrect={isChoose && result}
                isWrong={!checkResult(connections, item.key) && result}
                isReview={item.key == isTrue?.key}
              />
            );
          })}
        </View>

        <View style={styles.column}>
          {rightItems.map((item, idx) => {
            const isChoose = connections.some(
              conn => conn.right.key === item.key,
            );

            return (
              <ItemMatching
                ref={rightRefs.current[idx]}
                position={'right'}
                onPress={() => handleRightPress(item.key, idx, item.audio)}
                key={item.key}
                text={item.label || ''}
                url={item.image}
                keyItem={item.key}
                audio={item.audio}
                disable={checkResult(connections, item.key) && result}
                isSelected={isChoose}
                isCorrect={isChoose && result}
                isWrong={!checkResult(connections, item.key) && result}
                isReview={item.key == isTrue?.key}
              />
            );
          })}
        </View>
      </View>

      {!isDone && connections?.length == leftItems?.length && (
        <ButtonFooter
          disabled={connections?.length < leftItems?.length}
          btnCheck={handleSubmit}
          title={'Submit'}
        />
      )}
      {answerByStudent && totalMatched < 4 && (
        <View style={[styles.boxCorrect, {backgroundColor: theme.bg_primary}]}>
          <TextApp
            preset="text_md_semibold"
            text={'Click on any box to see the answer'}
            textColor={theme.text_quaternary}
            style={{textAlign: 'center'}}
          />
        </View>
      )}
      {isDone && !answerByStudent && (
        <View
          style={{
            width: '100%',
            height: 500,
            position: 'absolute',
            top: 200,
            zIndex: 99,
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: moderateVerticalScale(130),
    width: '100%',
    backgroundColor: '#fff',
  },

  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',

    alignItems: 'center',
    paddingHorizontal: moderateScale(20),
    paddingTop: moderateVerticalScale(30),
  },
  column: {
    justifyContent: 'space-between',
    gap: 20,
  },
  boxCorrect: {
    position: 'absolute',
    bottom: 0,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: moderateScale(32),
    paddingBottom: isAndroid
      ? moderateVerticalScale(16)
      : moderateVerticalScale(58),
    paddingTop: moderateVerticalScale(16),
    zIndex: 99999,
  },
});

export default MatchingPairsTemplate;
